import axios from "axios";
import qs from "qs";

export const getOAuthToken = async () => {
  try {
    const data = qs.stringify({
      grant_type: "client_credentials",
      client_id: `${process.env.OAUTH_CLIENT_ID}`,
      client_secret: `${process.env.OAUTH_CLIENT_SECRET}`,
      audience: `https://${process.env.OKTA_OAUTH_BASE_URL}/api/v2/`,
    });

    const response = await axios({
      method: "post",
      url: `https://${process.env.OKTA_OAUTH_BASE_URL}/oauth/token`,
      headers: {
        "content-type": "application/x-www-form-urlencoded",
      },
      data: data,
    });
    return response.data.access_token;
  } catch (error) {
    console.error("Error fetching access token:", error);
    return null;
  }
};
