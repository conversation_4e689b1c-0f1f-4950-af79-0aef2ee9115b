# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "1201a0737d1ac8980eca7bd1aa552960"
name = "nerivio_proxy_de"
handle = "nerivio_proxy_de"
application_url = "https://deproxy.nerivio.eu/"
embedded = true

[webhooks]
api_version = "2024-10"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_assigned_fulfillment_orders,read_discounts,read_merchant_managed_fulfillment_orders,read_third_party_fulfillment_orders,write_assigned_fulfillment_orders,write_customers,write_merchant_managed_fulfillment_orders,write_order_edits,write_orders,write_products,write_third_party_fulfillment_orders"

[auth]
redirect_urls = [
  "https://deproxy.nerivio.eu/auth/callback",
  "https://deproxy.nerivio.eu/auth/shopify/callback",
  "https://deproxy.nerivio.eu/api/auth/callback"
]

[app_proxy]
url = "https://deproxy.nerivio.eu/"
subpath = "pvs"
prefix = "apps"

[pos]
embedded = false
