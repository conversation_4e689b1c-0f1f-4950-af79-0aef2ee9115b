# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "1201a0737d1ac8980eca7bd1aa552960"
name = "nerivio_proxy_de"
handle = "nerivio_proxy_de"
application_url = "https://xt8bzebpse.execute-api.ap-south-1.amazonaws.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "nerivio-spain.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_assigned_fulfillment_orders,read_discounts,read_merchant_managed_fulfillment_orders,read_third_party_fulfillment_orders,write_assigned_fulfillment_orders,write_customers,write_merchant_managed_fulfillment_orders,write_order_edits,write_orders,write_products,write_third_party_fulfillment_orders"

[auth]
redirect_urls = [
  "https://xt8bzebpse.execute-api.ap-south-1.amazonaws.com/auth/callback",
  "https://xt8bzebpse.execute-api.ap-south-1.amazonaws.com/auth/shopify/callback",
  "https://xt8bzebpse.execute-api.ap-south-1.amazonaws.com/api/auth/callback"
]

[webhooks]
api_version = "2024-10"

[app_proxy]
url = "https://xt8bzebpse.execute-api.ap-south-1.amazonaws.com/"
subpath = "pvs"
prefix = "apps"

[pos]
embedded = false
