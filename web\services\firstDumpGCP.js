import axios from "axios";
import { Parser } from "json2csv";
import { Storage } from "@google-cloud/storage";
import path from "path";
import fs from "fs";
import { fileURLToPath } from "url";
import { getATFromSQL } from "../utils/utility.js";


// Get the directory path in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define paths
const CONFIG_DIR = path.join(__dirname, "..", "config");
const DATA_DIR = path.join(__dirname, "..", "data");

// Ensure data directory exists
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// Define the CSV file path in data directory
const CSV_FILE_PATH = path.join(DATA_DIR, "test.csv");

const getRechargeOrders = async () => {
  try {
    const storeData = await getATFromSQL();
    let rechargeToken;
    storeData.forEach((x) => {
      if (x.shop === process.env.SHOP) {
        rechargeToken = x.accessToken; // Using accessToken for recharge as well
      }
    });

    if (!rechargeToken) {
      throw new Error("Access token not found for the shop.");
    }

    let rechargeOrders = [];
    let nextCursor = null;
    let page = 1;
    do {
      const response = await axios.get(
        `https://api.rechargeapps.com/subscriptions`,
        {
          headers: {
            "X-Recharge-Version": "2021-11",
            "X-Recharge-Access-Token": rechargeToken,
            "Content-Type": "application/x-www-form-urlencoded",
          },
          params: {
            limit: 250, // max per Recharge docs
            ...(nextCursor ? { cursor: nextCursor } : {}),
          },
        }
      );

      const { subscriptions, next_cursor } = response.data;
      console.log(`Fetched page ${page}, count: ${subscriptions.length}`);
      console.log("next cursor = ", next_cursor);

      rechargeOrders.push(...subscriptions);
      nextCursor = next_cursor;
      page++;
    } while (nextCursor);

    return rechargeOrders;
  } catch (error) {
    console.error("Error fetching Recharge orders:", error.message);
    return [];
  }
};

const getShopifyOrders = async () => {
  try {
    const storeData = await getATFromSQL();
    let accessToken;

    storeData.forEach((x) => {
      if (x.shop === process.env.SHOP) {
        accessToken = x.accessToken;
      }
    });

    if (!accessToken) {
      throw new Error("Access token not found for the shop.");
    }

    const url = `https://${process.env.SHOP}/admin/api/2024-10/graphql.json`;

    let nextCursor = null;
    let page = 1;
    const allOrders = [];

    do {
    const graphqlQuery = {
      query: `
        {
          orders(first: 250${nextCursor ? `, after: "${nextCursor}"` : ""}) {
            edges {
              cursor
              node {
                id
                createdAt
                cancelledAt
                name
                refunds {
                  createdAt
                }
                customAttributes {
                  key
                  value
                }
              }
            }
          }
        }
      `,
    };

    try {
      const response = await axios.post(url, graphqlQuery, {
        headers: {
          "X-Shopify-Access-Token": accessToken,
          "Content-Type": "application/json",
        },
      });

      const { data } = response.data;
      const edges = data.orders.edges;

      console.log(`Fetched page ${page}, count: ${edges.length}`);

      const currentBatch = edges.map(({ node }) => {
        const refunds = node.refunds?.map((r) => r.createdAt) || [];

        const subscriptionAttr = node.customAttributes?.find(
          (attr) => attr.key === "rc_subscription_ids"
        );

        const orderData = {
          "Order Id": node.name || "-",
          "Order Date": node.createdAt || "-",
          "Cancellation Date": node.cancelledAt || "-",
          "Refund Dates": refunds || "-",
          "Subscription Status": "-",
          "Subscription Expiry Date": "-",
        };

        if (subscriptionAttr) {
          orderData.rc_subscription_id = subscriptionAttr.value;
        }

        return orderData;
      });

      allOrders.push(...currentBatch);

      // Update cursor for next round
      nextCursor = edges.length > 0 ? edges[edges.length - 1].cursor : null;
      page++;
    } catch (error) {
      console.error(
        "Error fetching orders:",
        error.response?.data || error.message
      );
      break;
    }
  } while (nextCursor);

  return allOrders;
  } catch (error) {
    console.error("Error fetching Shopify orders:", error.message);
    return [];
  }
};

const makeCsv = async () => {
  try {
    // gets all the recharge orders
    const rechargeOrders = await getRechargeOrders();

    // gets all the shopify orders
    const shopifyOrders = await getShopifyOrders();

    shopifyOrders.map((order) => {
      if (order.rc_subscription_id) {
        rechargeOrders.map((rcOrder) => {
          if (rcOrder.id == order.rc_subscription_id) {
            order["Subscription Status"] = rcOrder.status;
          }
        });
        delete order.rc_subscription_id;
      }
    });

    const json2csvParser = new Parser();
    const csv = json2csvParser.parse(shopifyOrders);

    fs.writeFileSync(CSV_FILE_PATH, csv);
    console.log(`CSV file saved at: ${CSV_FILE_PATH}`);
  } catch (error) {
    console.error("Error in makeCsv:", error.message);
    throw error;
  }
};

const upload = async () => {
  try {
    const storage = new Storage({
      keyFilename: path.join(CONFIG_DIR, "service-account.json"),
    });

    const bucketName = "bkt-eug-external-nerivio-shared";
    const destinationFilePath =
      "GetDeviceRecharge_Data/45TrailOrderData/test-upload.csv";
    const localFilePath = CSV_FILE_PATH;

    await storage.bucket(bucketName).upload(localFilePath, {
      destination: destinationFilePath,
    });
    console.log(
      `:white_check_mark: Test file uploaded to: gs://${bucketName}/${destinationFilePath}`
    );
  } catch (err) {
    console.error(":x: Upload failed:", err.message);
    throw err;
  }
};

const subscriptionGCP = async () => {
  try {
    await makeCsv();

    if (fs.existsSync(CSV_FILE_PATH)) {
      console.log("UPLOADING FILE");
      await upload();
    } else {
      console.log("CSV file not found");
      throw new Error("CSV file not found");
    }

    console.log("FILE UPLOADED NOW DELETING");

    fs.unlink(CSV_FILE_PATH, (err) => {
      if (err) {
        console.error("Error deleting file:", err);
        throw err;
      }
      console.log("File deleted successfully");
    });
  } catch (error) {
    console.error("Error in subscriptionGCP:", error.message);
    // Clean up the CSV file if it exists and there was an error
    if (fs.existsSync(CSV_FILE_PATH)) {
      fs.unlinkSync(CSV_FILE_PATH);
    }
    throw error;
  }
};

export default subscriptionGCP;