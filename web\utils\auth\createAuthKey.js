import CryptoJS from "crypto-js";

export const createAuthKey = async (data) => {
  const key = CryptoJS.enc.Utf8.parse(process.env.SECRET_KEY);
  const iv = CryptoJS.enc.Utf8.parse(process.env.SECRET_IV);

  const tokenPayload = {
    data, // Original data
    timestamp: Date.now(),
    nonce: CryptoJS.lib.WordArray.random(16).toString(),
  };

  const encrypted = CryptoJS.AES.encrypt(JSON.stringify(tokenPayload), key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });

  return encrypted.toString();
};
