import axios from "axios";
import { getATFromSQL } from "./utility.js";

export default async function fetchGraphqlDataShopify(query, variables) {
  try {
    let data = JSON.stringify({
      query: query,
      variables: variables,
    });

    let storeData = await getATFromSQL();
    let AT;
    storeData.map((x) => {
      if (x.shop === process.env.SHOP) {
        AT = x.accessToken;
      }
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.SHOP_URL}/admin/api/${process.env.GRAPH_QL_API_VERSION}/graphql.json`,
      headers: {
        "X-Shopify-Access-Token": AT,
        "Content-Type": "application/json",
      },
      data: data,
    };

    const response = await axios.request(config);
    return response.data;
  } catch (error) {
    console.log(error);
    return { error };
  }
}
