import { customerCreateWebhookMetaUpdateService } from "../services/customerCreate.servive.js";
import { updateCustomerDetailsService } from "../services/customerUpdate.service.js";

const customerCreateWebhookController = async (req, res) => {
  console.log("CUSTOMER CREATE WEBHOOK TRIGGER");
  console.log("req.body", req.body);

  res.sendStatus(200);

  const { id, email } = req.body;

  console.log("id , email", id, email);

  if (!email || !id) {
    return res.status(200).send({
      result: false,
      message: "missing email or Customer id ",
    });
  }

  try {
    let storeData = await customerCreateWebhookMetaUpdateService(id, email);

    console.log("customerCreateWebhook response", storeData);
  } catch (error) {
    console.error("Error updating customer details:", error);
    // res.status(500).send({ error: 'Internal server error' });
  }
};

export { customerCreateWebhookController };
