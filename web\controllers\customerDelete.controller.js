import { customerDeleteService } from "../services/customerDelete.service.js";
import { GetCustomer } from "../services/customerGet.service.js";

const customerDeleteController = async (req, res) => {
  console.log("CUSTOMER DELETE TRIGGER");
  const { email } = req.body;

  if (!email) {
    return res.status(200).send({
      result: false,
      message: "missing email to delete customer",
    });
  }

  try {
    // const { customerName, customerEmail } = await GetCustomer(customerId);
    // if (!customerEmail) {
    //   console.log("Customer not found in shopify");
    //   res.status(400).send({
    //     code: "01",
    //     result: false,
    //     error: "Customer not found in shopify, Deletion Failed",
    //   });
    //   return;
    // }
    let deleteEmailSent = await customerDeleteService(email);
    if (deleteEmailSent) {
      console.log("CUSTOMER DELETE result : ", deleteEmailSent);
      res.status(200).send({
        code: "00",
        result: true,
        message: "Delete User Email sent to <PERSON><PERSON>vio <PERSON>min",
      });
    } else {
      res.status(404).send({
        code: "01",
        result: false,
        error: "No email sent. Something went wrong.",
      });
    }
  } catch (error) {
    console.error("Error deleting customer :", error);
    res.status(500).send({
      code: "01",
      result: false,
      error: "Internal Server Error.",
    });
  }
};

export { customerDeleteController };
