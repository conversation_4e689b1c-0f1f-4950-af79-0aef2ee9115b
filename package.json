{"name": "nerivio-proxy-germany", "version": "1.0.0", "main": "web/index.js", "license": "UNLICENSED", "scripts": {"shopify": "shopify", "build": "shopify app build", "dev": "shopify app dev", "info": "shopify app info", "generate": "shopify app generate", "deploy": "shopify app deploy"}, "dependencies": {"nerivio-proxy-germany": "file:"}, "author": "prana", "private": true, "workspaces": ["extensions/*", "web", "web/frontend"]}