import axios from "axios";
import { getOAuthToken } from "../utils/generateOauthToken.js";
import dotenv from "dotenv";
import { getATFromSQL } from "../utils/utility.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";
import { ConvertToGID } from "../controllers/discount.controller.js";
import {
  EMAIL_CONSENT_SHOPIFY,
  SMS_CONSENT_SHOPIFY,
} from "../helpers/graphqlApis.js";
dotenv.config();

const updateCustomerCreateMetafieldsvalueUpdate = async (
  customerId,
  userId,
  isMinorConfirmed,
  isTermsAccepted,
  isSubscribed,
  AT
) => {
  const metafields = [];

  metafields.push(
    {
      ownerId: `gid://shopify/Customer/${customerId}`,
      key: "auth_0_id",
      value: userId,
      namespace: "custom",
      type: "single_line_text_field",
    },
    {
      ownerId: `gid://shopify/Customer/${customerId}`,
      key: "is_minor",
      value: isMinorConfirmed === "true" ? "true" : "false",
      namespace: "custom",
      type: "boolean",
    },
    {
      ownerId: `gid://shopify/Customer/${customerId}`,
      key: "is_terms_accepted",
      value: isTermsAccepted === "true" ? "true" : "false",
      namespace: "custom",
      type: "boolean",
    },
    {
      ownerId: `gid://shopify/Customer/${customerId}`,
      key: "is_subscribed",
      value: isSubscribed === "true" ? "true" : "false",
      namespace: "custom",
      type: "boolean",
    }
  );

  const metafieldsInput = metafields
    .map(
      (field) => `
      {
        ownerId: "${field.ownerId}",
        key: "${field.key}",
        value: "${field.value}",
        namespace: "${field.namespace}",
        type: "${field.type}"
      }
    `
    )
    .join(",");

  const query = `
      mutation {
        metafieldsSet(metafields: [${metafieldsInput}]) {
          userErrors {
            field
            message
          }
          metafields {
            id
            createdAt
            namespace
            type
            key
            value
          }
        }
      }
    `;

  try {
    const response = await axios({
      method: "post",
      url: `https://${process.env.SHOP}/admin/api/${process.env.GRAPH_QL_API_VERSION}/graphql.json`,
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": AT,
      },
      data: JSON.stringify({ query }),
    });

    return response.data;
  } catch (error) {
    console.error("Error updating metafields:", error);
    return null;
  }
};

export const customerCreateWebhookMetaUpdateService = async (
  customerId,
  email
) => {
  let storeData = await getATFromSQL();
  let AT;
  storeData.map((x) => {
    if (x.shop === process.env.SHOP) {
      AT = x.accessToken;
    }
  });

  try {
    const userData = await getUserDataByEmail(email);
    const userId = userData[0]?.user_id;
    const isMinorConfirmed =
      userData[0]?.user_metadata?.is_minor_confirmed || "false";
    const isTermsAccepted = userData[0]?.user_metadata?.is_terms_accepted;
    const isSubscribed = userData[0]?.user_metadata?.is_subscribed;

    // console.log("userData", userData, "userId", userId, "isMinorConfirmed", isMinorConfirmed, "isSocial", isSocial);
    console.log("UserData : ", JSON.stringify(userData));
    console.log("userId : ", JSON.stringify(userId));
    console.log("isMinorConfirmed : ", JSON.stringify(isMinorConfirmed));
    console.log("isTermsAccepted : ", JSON.stringify(isTermsAccepted));
    console.log("isSubscribed : ", JSON.stringify(isSubscribed));

    const metafieldsResult = await updateCustomerCreateMetafieldsvalueUpdate(
      customerId,
      userId,
      isMinorConfirmed,
      isTermsAccepted,
      isSubscribed,
      AT
    );

    console.log(
      "metafieldsResult : ",
      JSON.stringify(metafieldsResult, undefined, 2)
    );
    if (isSubscribed === "true") {
      await UpdateSMSEmailMarketing(customerId);
    }

    return {
      result: true,
      message: "Meta successfully updated",
      data: JSON.stringify(metafieldsResult),
    };
  } catch (error) {
    console.error("Error linking prescription with order:", error);
    throw new Error("Failed to link prescription with order");
  }
};

// Function to get user data from Auth0 by email
export const getUserDataByEmail = async (email) => {
  let FetchedBearerToken = await getOAuthToken();
  try {
    const response = await axios({
      method: "get",
      url: `https://${
        process.env.OKTA_OAUTH_BASE_URL
      }/api/v2/users-by-email?email=${encodeURIComponent(email)}`,
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${FetchedBearerToken}`,
      },
    });

    console.log(response);

    const userData = response.data;
    // console.log("userData", userData)
    return userData;
  } catch (error) {
    console.error(
      "Error fetching user data:",
      error.response ? error.response.data : error.message
    );
    throw new Error("Failed to fetch user data");
  }
};

async function UpdateSMSEmailMarketing(customerId) {
  try {
    const shopifyCustomerId = ConvertToGID(customerId, "Customer");
    const smsConsentResponse = await fetchGraphqlDataShopify(
      SMS_CONSENT_SHOPIFY,
      {
        input: {
          customerId: `${shopifyCustomerId}`,
          smsMarketingConsent: {
            marketingOptInLevel: "SINGLE_OPT_IN",
            marketingState: "SUBSCRIBED",
          },
        },
      }
    );
    const emailConsentResponse = await fetchGraphqlDataShopify(
      EMAIL_CONSENT_SHOPIFY,
      {
        input: {
          customerId: `${shopifyCustomerId}`,
          emailMarketingConsent: {
            marketingOptInLevel: "SINGLE_OPT_IN",
            marketingState: "SUBSCRIBED",
          },
        },
      }
    );

    let customerSmsUserErrors =
      smsConsentResponse?.data?.customerSmsMarketingConsentUpdate?.userErrors;
    let customerEmailUserErrors =
      emailConsentResponse?.data?.customerEmailMarketingConsentUpdate
        ?.userErrors;

    if (
      customerSmsUserErrors?.length !== 0 &&
      customerEmailUserErrors?.length !== 0
    ) {
      console.log(
        "Customer SMS OR Email Consent User Errors : ",
        customerSmsUserErrors,
        customerEmailUserErrors
      );
      return false;
    } else {
      console.log(
        `Customer ID ${shopifyCustomerId} consent Meta, Marketing-Consents Updated successfully`
      );
      return true;
    }
  } catch (error) {
    console.log("Error in UpdateSMSEmailMarketing : ", error);
    return false;
  }
}
