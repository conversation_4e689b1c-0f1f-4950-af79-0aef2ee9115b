import { customerPasswordResetService } from "../services/customerPasswordReset.service.js";

const customerPasswordResetController = async (req, res) => {
  console.log("CUSTOMER PASSWORD TRIGGER");
  const { email } = req.body;

  if (!email) {
    return res.status(200).send({
      result: false,
      message: "missing email to reset password",
    });
  }
  try {
    let resetEmailSent = await customerPasswordResetService(email);
    if (resetEmailSent) {
      console.log("CUSTOMER PASSWORD result : ", resetEmailSent);
      res.status(200).send({
        code: "00",
        result: true,
        message: resetEmailSent,
      });
    } else {
      res.status(404).send({
        code: "01",
        result: false,
        error: "No email sent. Something went wrong.",
      });
    }
  } catch (error) {
    console.error("Error updating customer details:", error);
    res.status(500).send({
      code: "01",
      result: false,
      error: "Internal Server Error.",
    });
  }
};

export { customerPasswordResetController };
