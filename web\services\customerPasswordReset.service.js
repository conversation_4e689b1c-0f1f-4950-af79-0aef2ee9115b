import axios from "axios";
import { getOAuthToken } from "../utils/generateOauthToken.js";
import emailService from "../utils/emailService.js";

const customerPasswordResetService = async (email) => {
  let FetchedBearerToken = await getOAuthToken();
  try {
    const response = await axios({
      method: "post",
      url: `https://${process.env.OKTA_OAUTH_BASE_URL}/dbconnections/change_password`,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${FetchedBearerToken}`,
      },
      data: {
        client_id: `${process.env.OAUTH_CLIENT_ID}`,
        email: email,
        connection: "Username-Password-Authentication",
      },
    });

    const responseMessage = response?.data;
    if (
      responseMessage &&
      responseMessage === "We've just sent you an email to reset your password."
    ) {
      console.log("AUTH 0 OKTA sent the email.");
      return responseMessage;
    } else {
      // await emailService.sendEmail(
      //   "passwordReset",
      //   {
      //     customer: email,
      //     token: FetchedBearerToken,
      //   },
      //   email
      // );
      console.log("Something went wrong with the AUTH 0 Reset Password API");
      return null;
    }
  } catch (error) {
    console.error("Error sending password reset email:", error);
    return null;
  }
};

export { customerPasswordResetService };
