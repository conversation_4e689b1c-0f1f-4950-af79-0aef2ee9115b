import axios from "axios";

async function fetchMediaImage(shopData, mediaImageGid) {
  console.log("storeUrl", shopData, "mediaImageGid", mediaImageGid);

  const SHOP = shopData[0].shop;
  const AT = shopData[0].accessToken;

  try {
    const response = await axios({
      method: "post",
      url: `https://${SHOP}/admin/api/${process.env.GRAPH_QL_API_VERSION}/graphql.json`,
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": `${AT}`,
      },
      data: {
        query: `
                {
                    node(id: "${mediaImageGid}") {
                        ... on MediaImage {
                            image {
                                url
                            }
                        }
                    }
                }
                `,
      },
    });

    console.log(response.data);
    return response.data;
  } catch (error) {
    console.error("Error fetching media image:", error);
    throw error;
  }
}

// const storeUrl = 'storecreditdev.myshopify.com';
// const mediaImageGid = 'gid://shopify/MediaImage/**************';

// fetchMediaImage(storeUrl, mediaImageGid)
//     .then(data => console.log('Media Image URL:', data.data.node.image.url))
//     .catch(error => console.error(error));
