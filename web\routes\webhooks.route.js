import express from "express";
import { Router } from "express";
import { linkPrescriptionWithOrderController } from "../controllers/prescriptionOrderLink.controller.js";
import { customerCreateWebhookController } from "../controllers/customerCreateWebhook.controller.js";
import verifyWebhook from "../middlewares/webhookVerify.js";
import jsonParser from "../middlewares/jsonParser.js";

const webhookRouter = Router();

// WEBHOOK ROUTES/ENDPOINTS
webhookRouter.post(
  "/customerCreateWebhook",
  express.raw({ type: "application/json" }),
  verifyWebhook,
  jsonParser,
  customerCreateWebhookController
);

//ORDER CREATE WEBHOOK RESPONSE
webhookRouter.post(
  "/linkPrescriptionWithOrder",
  express.raw({ type: "application/json" }),
  verifyWebhook,
  jsonParser,
  linkPrescriptionWithOrderController
);

export default webhookRouter;
