const GET_DISCOUNT_ID_BY_CODE = `query GetDiscountID ($code: String!) {
  codeDiscountNodeByCode(code: $code) {
    id
    codeDiscount {
      ... on DiscountCodeApp {
        asyncUsageCount
      }
    }
  }
}
`;

const SET_META = `mutation SetMeta ($metafields: [MetafieldsSetInput!]!) {
metafieldsSet(metafields: $metafields) {
  userErrors {
    field
    message
  }
  metafields {
    id
    key
    namespace
    type
    value
  }
}
}`;

const GET_CUSTOMER_BY_ID = `query GetCustomer ($id: ID!) {
  customer(id: $id) {
    id
    email
    firstName
    lastName
  }
}
`;

const SMS_CONSENT_SHOPIFY = `mutation customerSmsMarketingConsentUpdate($input: CustomerSmsMarketingConsentUpdateInput!) {
  customerSmsMarketingConsentUpdate(input: $input) {
    customer {
      id
    }
    userErrors {
      field
      message
    }
  }
}
`;

const EMAIL_CONSENT_SHOPIFY = `mutation customerEmailMarketingConsentUpdate($input: CustomerEmailMarketingConsentUpdateInput!) {
  customerEmailMarketingConsentUpdate(input: $input) {
    customer {
      id
    }
    userErrors {
      field
      message
    }
  }
}
`;

const DELETE_CUSTOMER_BY_ID = `mutation deleteCustomer($id: ID!) {
  customerDelete(input: { id: $id }) {
    deletedCustomerId
  }
}
`;

const GET_VARIANT_BY_ID = `query variantById($id: ID!) {
  productVariant(id: $id) {
    id
    sku
    title
    inventoryQuantity
    price
    availableForSale
    product {
      description(truncateAt: 250)
      images(first: 10) {
        edges {
          node {
            id
            originalSrc
            src
          }
        }
      }
    }
  }
}
`;

const UPDATE_CUSTOMER_PHONE = `mutation UpdatePhone ($id: ID!, $phone: String!) {
  customerUpdate(input: {id: $id, phone: $phone}) {
    customer {
      id
    }
    userErrors {
      field
      message
    }
  }
}
`;

export {
  GET_DISCOUNT_ID_BY_CODE,
  SET_META,
  GET_CUSTOMER_BY_ID,
  SMS_CONSENT_SHOPIFY,
  EMAIL_CONSENT_SHOPIFY,
  DELETE_CUSTOMER_BY_ID,
  GET_VARIANT_BY_ID,
  UPDATE_CUSTOMER_PHONE,
};
