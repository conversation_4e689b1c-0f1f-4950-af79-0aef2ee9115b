import { getOrderInvoiceService } from "../services/orderInvoice.service.js";

const getOrderInvoiceController = async (req, res) => {
  try {
    console.log("GET ORDER INVOICE TRIGGERED");
    const { orderNumber } = req.body;

    if (!orderNumber) {
      return res.status(400).send({
        code: "01",
        result: false,
        error: "Missing orderNumber to get order invoice",
      });
    }

    let orderInvoice = await getOrderInvoiceService(orderNumber);
    if (orderInvoice) {
      console.log("GET ORDER INVOICE result : ", orderInvoice);
      res.status(200).json({ message: "Success", data: orderInvoice });
    } else {
      res.status(404).send({
        code: "01",
        result: false,
        error: `Order Invoice not found for orderNumber : ${orderNumber}`,
      });
    }
  } catch (error) {
    console.error("Error fetching order invoice :", error);
    res.status(500).send({
      code: "01",
      result: false,
      error: "Internal Server Error.",
    });
  }
};

export { getOrderInvoiceController };
