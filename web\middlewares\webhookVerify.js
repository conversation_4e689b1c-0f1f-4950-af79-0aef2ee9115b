import crypto from "crypto";

export default function verifyWebhook(req, res, next) {
  const hmac = req.headers["x-shopify-hmac-sha256"];
  const sharedSecret = process.env.SHOPIFY_API_SECRET;

  if (!hmac) {
    console.log("HMAC is missing");
    return res.status(401).send({ error: "unauthorized" });
  }

  const rawBody = req.body;
  const calculatedSignature = crypto
    .createHmac("sha256", sharedSecret)
    .update(rawBody, "utf8")
    .digest("base64");

  if (calculatedSignature !== hmac) {
    console.log("Webhook Not Verified");
    return res.status(401).send({ error: "unauthorized" });
  } else {
    console.log("Webhook Verified");
    next();
  }
}
