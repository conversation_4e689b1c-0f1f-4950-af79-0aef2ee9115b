import { customerCreateWebhookMetaUpdateService } from "../services/customerCreate.servive.js";
import {
  updateCustomerDetailsService,
  updateCustomerPAPService,
} from "../services/customerUpdate.service.js";

const updateCustomerDetailsController = async (req, res) => {
  console.log("CUSTOMER UPDATE API TRIGGER HELLOO");
  // console.log(req)
  const { customerId, firstName, lastName, phone, dateofbirth } = req.body;

  console.log(
    "customerId, firstName, lastName, phone, dateofbirth",
    customerId,
    firstName,
    lastName,
    phone,
    dateofbirth
  );

  if (!customerId || !firstName || !lastName || !phone) {
    return res.status(200).send({
      result: false,
      message: "missing OR wrong input fields",
    });
  }

  try {
    let storeData = await updateCustomerDetailsService(
      customerId,
      firstName,
      lastName,
      phone,
      dateofbirth
    );

    console.log("Customer update storeData", storeData);

    res.status(200).json(storeData);
  } catch (error) {
    console.error("Error updating customer details:", error);
    res.status(500).send({ error: "Internal server error" });
  }
};

const updateCustomerPAPController = async (req, res) => {
  console.log("CUSTOMER UPDATE PAP API TRIGGER");
  // console.log(req)
  const { customerId } = req.body;

  console.log("customerId", customerId);

  if (!customerId) {
    return res.status(200).send({
      result: false,
      message: "missing OR wrong input fields",
    });
  }

  try {
    let storeData = await updateCustomerPAPService(customerId);
    res.status(200).json(storeData);
  } catch (error) {
    console.error("Error updating customer details:", error);
    res.status(500).send({ error: "Internal server error" });
  }
};

export { updateCustomerDetailsController, updateCustomerPAPController };
