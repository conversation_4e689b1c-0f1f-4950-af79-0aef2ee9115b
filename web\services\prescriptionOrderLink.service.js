import axios from "axios";

import { createAuthKey } from "../utils/auth/createAuthKey.js";

const linkPrescriptionWithOrderService = async (
  orderId,
  prescriptionDocRefId
) => {
  try {
    const token = await createAuthKey(process.env.SECRET_KEY);
    const locale = process.env.LOCALE;
    const storeKey = process.env.STOREKEY;
    const response = await axios.post(
      `https://${process.env.PVS_BASE_URL}/nerivio/api/orders/${orderId}?prescriptionDocRefId=${prescriptionDocRefId}&locale=${locale}&source=SHOPIFY&storeKey=${storeKey}`,
      {},
      {
        headers: {
          "x-encrypted-key": `${token}`,
          "app-region": process.env.CURRENT_REGION,
        },
      }
    );
    // console.log("response ", response.data)
    return {
      result: true,
      message: "Prescription linked with order successfully",
      data: response.data,
    };
  } catch (error) {
    console.error("Error linking prescription with order:", error);
    throw new Error("Failed to link prescription with order");
  }
};

export { linkPrescriptionWithOrderService };
