// import { uploadPrescriptionService } from "../services/prescriptionUpload.service.js";
// import fs from "fs";

// const uploadCustomerPrescriptionController = async (req, res) => {
//   console.log("UPLOAD PRESCRIPTION TRIGGERED");
//   const { customerId } = req.body;
//   const file = req.file;

//   console.log("customerId :", customerId, " file :", file);

//   if (!customerId || !file) {
//     return res.status(400).json({
//       code: "01",
//       result: false,
//       message: "Missing customerId or prescription file.",
//     });
//   }

//   try {
//     const response = await uploadPrescriptionService(customerId, file.path);

//     console.log("Upload customer prescription response", response);

//     return res.status(200).json(response);
//   } catch (error) {
//     console.error("Error uploading prescription:", error);
//     return res
//       .status(500)
//       .json({ code: "01", result: false, message: "Internal server error" });
//   } finally {
//     // Ensure the file is deleted, even if an error occurs
//     // fs.unlink(file.path, (err) => {
//     //   if (err) {
//     //     console.error("Failed to delete uploaded file:", err);
//     //   } else {
//     //     console.log("Uploaded file deleted successfully");
//     //   }
//     // });
//   }
// };

// export { uploadCustomerPrescriptionController };

import { uploadPrescriptionService } from "../services/prescriptionUpload.service.js";

const uploadCustomerPrescriptionController = async (req, res) => {
  console.log("UPLOAD PRESCRIPTION TRIGGERED");
  const { customerId } = req.body;
  const file = req.file;

  console.log("customerId :", customerId, " file :", file);

  if (!customerId || !file) {
    return res.status(400).json({
      code: "01",
      result: false,
      message: "Missing customerId or prescription file.",
    });
  }

  try {
    const response = await uploadPrescriptionService(customerId, file);
    console.log("Upload customer prescription response", response);
    res.status(200).json(response);
  } catch (error) {
    console.error("Error uploading prescription:", error);
    res.status(500).send({ error: "Internal server error" });
  }
};

export { uploadCustomerPrescriptionController };
