import { join } from "path";
import { readFileSync } from "fs";
import express from "express";
import serveStatic from "serve-static";
import shopify from "./shopify.js";
import PrivacyWebhookHandlers from "./privacy.js";
import pvsRouter from "./routes/customerPVS.route.js";
import { orderCreationWebhook } from "./webhooks/orderCreateWebhook.js";
import { customerCreationWebhook } from "./webhooks/customerCreateWebhook.js";
import cors from "cors";
import * as dotenv from "dotenv";
import discountRouter from "./routes/discount.route.js";
import webhookRouter from "./routes/webhooks.route.js";
import subscriptionGCP from "./services/subscriptionGCP.service.js";
import cron from "node-cron";
dotenv.config();

const PORT = parseInt(
  process.env.BACKEND_PORT || process.env.PORT || "3000",
  10
);

const STATIC_PATH =
  process.env.NODE_ENV === "production"
    ? `${process.cwd()}/frontend/dist`
    : `${process.cwd()}/frontend/`;

const app = express();
app.use(cors());

await orderCreationWebhook();
await customerCreationWebhook();
app.use("/api/webhook", webhookRouter);

app.use(express.json());

app.get("/api/healthcheck", (req, res) => {
  res.send("Health Check API working ok !!!");
});

app.use("/pr", pvsRouter);
app.use("/discount", discountRouter);

app.get(shopify.config.auth.path, shopify.auth.begin());
app.get(
  shopify.config.auth.callbackPath,
  shopify.auth.callback(),
  shopify.redirectToShopifyOrAppRoot()
);

app.post(
  shopify.config.webhooks.path,
  shopify.processWebhooks({ webhookHandlers: PrivacyWebhookHandlers })
);

// If you are adding routes outside of the /api path, remember to
// also add a proxy rule for them in web/frontend/vite.config.js

app.use("/api/*", shopify.validateAuthenticatedSession());
app.use(shopify.cspHeaders());
app.use(serveStatic(STATIC_PATH, { index: false }));

app.use("/*", shopify.ensureInstalledOnShop(), async (_req, res, _next) => {
  return res
    .status(200)
    .set("Content-Type", "text/html")
    .send(
      readFileSync(join(STATIC_PATH, "index.html"))
        .toString()
        .replace("%VITE_SHOPIFY_API_KEY%", process.env.SHOPIFY_API_KEY || "")
    );
});

const CRON_SCHEDULE = "0 0 * * *"; // Runs every day at 12:00 AM
// const CRON_SCHEDULE = "0 0 5 1 * *"; // Runs 1st of every month at 5:00 AM

cron.schedule(CRON_SCHEDULE, async () => {
  console.log("Running subscriptionGCP cron job:", new Date().toISOString());
  try {
    await subscriptionGCP();
  } catch (error) {
    console.error("Error in subscriptionGCP cron job:", error);
  }
});

app.listen(PORT, () => {
  console.log(`Server is running on port 🚀 ${PORT}`);
  console.log(
    `Subscription GCP cron job scheduled with pattern: ${CRON_SCHEDULE}`
  );
});
