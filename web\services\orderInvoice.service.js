import axios from "axios";
import { createAuth<PERSON>ey } from "../utils/auth/createAuthKey.js";

export const getOrderInvoiceService = async (orderNumber) => {
  try {
    const token = await createAuthKey(process.env.SECRET_KEY);
    const config = {
      method: "post",
      url: `https://${process.env.PVS_BASE_URL}/nerivio/api/invoice`,
      headers: {
        "x-encrypted-key": `${token}`,
        "app-region": process.env.CURRENT_REGION,
      },
      params: {
        externalOrderNumber: orderNumber,
        storeKey: process.env.STOREKEY,
      },
    };

    console.log(
      "Config Headers: ",
      JSON.stringify(config.headers, undefined, 2)
    );

    const response = await axios.request(config);

    if (response.status === 200) {
      return response.data;
    } else {
      throw new Error("Failed to fetch order invoice");
    }
  } catch (error) {
    console.error("Error fetching order invoice :", error);
    throw new Error("Failed to fetch order invoice");
  }
};
