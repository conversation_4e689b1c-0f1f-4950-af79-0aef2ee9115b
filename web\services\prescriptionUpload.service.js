// import axios from "axios";
// import FormData from "form-data";
// import fs from "fs";
// import { createAuth<PERSON>ey } from "../utils/auth/createAuthKey.js";

// export const uploadPrescriptionService = async (customerId, filePath) => {
//   try {
//     const token = await createAuthKey(process.env.SECRET_KEY);
//     const formData = new FormData();

//     // Read file from disk
//     formData.append("prescriptionDocument", fs.createReadStream(filePath));

//     let config = {
//       method: "post",
//       url: `https://${process.env.PVS_BASE_URL}/nerivio/api/customers/${customerId}/prescription`,
//       headers: {
//         "x-encrypted-key": `${token}`,
//         "app-region": process.env.CURRENT_REGION,
//         ...formData.getHeaders(), // Important for multipart data
//       },
//       data: formData,
//     };

//     const response = await axios.request(config);

//     return {
//       code: "00",
//       result: true,
//       message: "Prescription uploaded successfully",
//       data: response.data,
//     };
//   } catch (error) {
//     console.error(
//       "Error uploading prescription in service:",
//       error.response ? error.response.data : error.message
//     );
//     console.error(
//       "Error : ",
//       error
//     );
//     throw new Error("Failed to upload prescription to external service");
//   }
// };

import axios from "axios";
import FormData from "form-data";

import { createAuthKey } from "../utils/auth/createAuthKey.js";

export const uploadPrescriptionService = async (customerId, file) => {
  try {
    const token = await createAuthKey(process.env.SECRET_KEY);
    const formData = new FormData();

    formData.append("prescriptionDocument", file.buffer, file.originalname);

    let config = {
      method: "post",
      url: `https://${process.env.PVS_BASE_URL}/nerivio/api/customers/${customerId}/prescription`,
      headers: {
        "x-encrypted-key": `${token}`,
        "app-region": process.env.CURRENT_REGION,
        ...formData.getHeaders(), // Important for multipart data
      },
      data: formData,
    };

    console.log("Config Headers: ", JSON.stringify(config.headers, undefined, 2));

    const response = await axios.request(config);

    return {
      code: "00",
      result: true,
      message: "Prescription uploaded successfully",
      data: response.data,
    };
  } catch (error) {
    console.error(
      "Error uploading prescription in service:",
      error.response ? error.response.data : error.message
    );
    throw new Error("Failed to upload prescription to external service");
  }
};
