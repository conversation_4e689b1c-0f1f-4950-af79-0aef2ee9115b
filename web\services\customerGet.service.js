import { ConvertToGID } from "../controllers/discount.controller.js";
import { GET_CUSTOMER_BY_ID } from "../helpers/graphqlApis.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";

export async function GetCustomer(customerId) {
  try {
    const customerGid = ConvertToGID(customerId, "Customer");
    const response = await fetchGraphqlDataShopify(GET_CUSTOMER_BY_ID, {
      id: customerGid,
    });

    const customerResId = response?.data?.customer?.id;

    if (customerResId) {
      const firstName = response?.data?.customer?.firstName;
      const lastName = response?.data?.customer?.lastName;
      const customerEmail = response?.data?.customer?.email;
      const customerName = combineName(firstName, lastName);
      return { customerName, customerEmail };
    }
    return { customerName: null, customerEmail: null };
  } catch (error) {
    console.log("Error in GetCustomer : ", error);
    return { customerName: null, customerEmail: null };
  }
}

export function combineName(firstName, lastName) {
  return `${firstName} ${lastName}`.trim();
}
