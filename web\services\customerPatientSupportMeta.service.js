import axios from "axios";
import { getATFromSQL } from "../utils/utility.js";
import { dobValidator } from "../utils/validators.js";
import emailService from "../utils/emailService.js";
import { GetCustomer } from "./customerGet.service.js";

const customerPatientSupportOptMetafieldService = async (
  customerId,
  supportConsent,
  bookedSlotTime,
  bookedSlotDate,
  optOutReason
) => {
  let storeData = await getATFromSQL();
  let AT;
  storeData.map((x) => {
    if (x.shop === process.env.SHOP) {
      AT = x.accessToken;
    }
  });

  const metafields = [];
  const deleteMetafields = [];

  if (supportConsent === "true" && bookedSlotTime && bookedSlotDate) {
    if (!dobValidator(bookedSlotDate)) {
      return {
        success: false,
        message: "Dob missing or dob format wrong / should be YYYY-MM-DD",
      };
    } else {
      const supportConsentDate = getCurrentDate();
      metafields.push(
        {
          ownerId: `gid://shopify/Customer/${customerId}`,
          key: "support_consent",
          value: supportConsent,
          namespace: "custom",
          type: "boolean",
        },
        {
          ownerId: `gid://shopify/Customer/${customerId}`,
          key: "bookedSlotTime",
          value: bookedSlotTime,
          namespace: "custom",
          type: "single_line_text_field",
        },
        {
          ownerId: `gid://shopify/Customer/${customerId}`,
          key: "bookedSlotDate",
          value: bookedSlotDate,
          namespace: "custom",
          type: "date",
        },
        {
          ownerId: `gid://shopify/Customer/${customerId}`,
          key: "support_consent_date",
          value: supportConsentDate,
          namespace: "custom",
          type: "date",
        }
      );
      // Delete the optOutReason metafield if supportConsent/opt in is true
      deleteMetafields.push({
        ownerId: `gid://shopify/Customer/${customerId}`,
        namespace: "custom",
        key: "optOutReason",
      });
    }
  } else if (supportConsent === "false" && optOutReason) {
    metafields.push(
      {
        ownerId: `gid://shopify/Customer/${customerId}`,
        key: "support_consent",
        value: supportConsent,
        namespace: "custom",
        type: "boolean",
      },
      {
        ownerId: `gid://shopify/Customer/${customerId}`,
        key: "optOutReason",
        value: optOutReason,
        namespace: "custom",
        type: "single_line_text_field",
      }
    );
    // Delete bookedSlotTime and bookedSlotDate metafields if supportConsent/opt in is false
    deleteMetafields.push(
      {
        ownerId: `gid://shopify/Customer/${customerId}`,
        namespace: "custom",
        key: "bookedSlotTime",
      },
      {
        ownerId: `gid://shopify/Customer/${customerId}`,
        namespace: "custom",
        key: "bookedSlotDate",
      },
      {
        ownerId: `gid://shopify/Customer/${customerId}`,
        namespace: "custom",
        key: "support_consent_date",
      }
    );
  } else {
    console.log(
      "Customer Opt in program / No condition met to update metafield"
    );
    return;
  }

  const metafieldsInput = metafields
    .map(
      (field) => `
    {
      ownerId: "${field.ownerId}",
      key: "${field.key}",
      value: "${field.value}",
      namespace: "${field.namespace}",
      type: "${field.type}"
    }
  `
    )
    .join(",");

  const deleteMetafieldsInput = deleteMetafields
    .map(
      (field) => `
    {
      ownerId: "${field.ownerId}",
      namespace: "${field.namespace}",
      key: "${field.key}"
    }
  `
    )
    .join(",");

  const query = `
    mutation {
      metafieldsSet(metafields: [${metafieldsInput}]) {
        userErrors {
          field
          message
        }
        metafields {
          id
          createdAt
          namespace
          type
          key
          value
        }
      }
      metafieldsDelete(metafields: [${deleteMetafieldsInput}]) {
        userErrors {
          field
          message
        }
        deletedMetafields {
          key
          namespace
          ownerId
        }
      }
    }
  `;

  try {
    const response = await axios({
      method: "post",
      url: `https://${process.env.SHOP}/admin/api/${process.env.GRAPH_QL_API_VERSION}/graphql.json`,
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": AT,
      },
      data: JSON.stringify({ query }),
    });

    const { customerName, customerEmail } = await GetCustomer(customerId);
    if (!customerEmail) {
      console.log("Email for the customer not found...");
      return response.data;
    }

    if (supportConsent === "true") {
      //Enrollment Email
      emailService.sendEmail(
        "supportEnrollmentConfirmation",
        {
          patientName: customerName,
        },
        customerEmail
      );
    } else if (supportConsent === "false") {
      // Unenrollment Email
      emailService.sendEmail(
        "supportUnenrollmentConfirmation",
        {
          patientName: customerName,
        },
        customerEmail
      );
    }

    return response.data;
  } catch (error) {
    console.error("Error updating metafields:", error);
    return null;
  }
};

const getCurrentDate = () => {
  const today = new Date();

  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0"); // Months are zero-based
  const day = String(today.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};

export { customerPatientSupportOptMetafieldService };
