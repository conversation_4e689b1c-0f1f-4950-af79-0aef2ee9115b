import { customerPatientSupportOptMetafieldService } from "../services/customerPatientSupportMeta.service.js";

const customerPatientSupportOptMetafieldController = async (req, res) => {
  console.log("CUSTOMER PATIENT METAFIELD TRIGGER", req.body);

  const { customerId, bookedSlotTime, bookedSlotDate, optOutReason } = req.body;
  const { supportConsent } = req.query;

  console.log(
    "customerId , bookedSlotTime , bookedSlotDate , optOutReason , supportConsent",
    customerId,
    bookedSlotTime,
    bookedSlotDate,
    optOutReason,
    supportConsent
  );

  if (!customerId && (supportConsent == "true" || supportConsent == "false")) {
    return res.status(200).send({
      result: false,
      message: "missing OR wrong input fields",
    });
  }
  try {
    let storeData = await customerPatientSupportOptMetafieldService(
      customerId,
      supportConsent,
      bookedSlotTime,
      bookedSlotDate,
      optOutReason
    );
    console.log("customerPatientSupportOpt result", JSON.stringify(storeData));
    res.status(200).json({ message: "Success", data: storeData });
  } catch (error) {
    console.error("Error updating customer details:", error);
    res.status(500).send({ error: "Internal server error" });
  }
};

export { customerPatientSupportOptMetafieldController };
