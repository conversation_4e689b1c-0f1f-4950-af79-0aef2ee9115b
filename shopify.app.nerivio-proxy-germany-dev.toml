# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "309de086194ec360ee7557dec847ae02"
name = "nerivio_proxy_de_uat"
handle = "nerivio_proxy_de_uat"
application_url = "https://lasting-lg-damaged-requests.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "checkout-functions-test.myshopify.com"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_assigned_fulfillment_orders,read_discounts,read_merchant_managed_fulfillment_orders,read_third_party_fulfillment_orders,write_assigned_fulfillment_orders,write_customers,write_merchant_managed_fulfillment_orders,write_order_edits,write_orders,write_products,write_third_party_fulfillment_orders"

[auth]
redirect_urls = [
  "https://lasting-lg-damaged-requests.trycloudflare.com/auth/callback",
  "https://lasting-lg-damaged-requests.trycloudflare.com/auth/shopify/callback",
  "https://lasting-lg-damaged-requests.trycloudflare.com/api/auth/callback"
]

[webhooks]
api_version = "2024-10"

[app_proxy]
url = "https://lasting-lg-damaged-requests.trycloudflare.com"
subpath = "pvs"
prefix = "apps"

[pos]
embedded = false
