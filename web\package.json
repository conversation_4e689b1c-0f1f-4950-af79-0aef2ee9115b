{"name": "shopify-app-template-node", "version": "1.0.0", "private": true, "license": "UNLICENSED", "scripts": {"debug": "node --inspect-brk index.js", "dev": "cross-env NODE_ENV=development nodemon index.js --ignore ./frontend", "serve": "cross-env NODE_ENV=production node index.js"}, "type": "module", "engines": {"node": ">=16.13.0"}, "dependencies": {"@google-cloud/storage": "^7.16.0", "@sendgrid/mail": "^8.1.3", "@shopify/shopify-app-express": "^5.0.4", "@shopify/shopify-app-session-storage-sqlite": "^4.0.4", "axios": "^1.9.0", "compression": "^1.7.4", "cors": "^2.8.5", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "json2csv": "^6.0.0-alpha.2", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.15", "qs": "^6.13.0", "serve-static": "^1.14.1", "shopify-app-template-node": "file:"}, "devDependencies": {"nodemon": "^2.0.15", "prettier": "^2.6.2", "pretty-quick": "^3.1.3"}}