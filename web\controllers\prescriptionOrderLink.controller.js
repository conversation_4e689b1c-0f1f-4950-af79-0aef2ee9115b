import axios from "axios";
import crypto from "crypto";
import {
  GET_VARIANT_BY_ID,
  SET_META,
  UPDATE_CUSTOMER_PHONE,
} from "../helpers/graphqlApis.js";
import { linkPrescriptionWithOrderService } from "../services/prescriptionOrderLink.service.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";
import { getATFromSQL } from "../utils/utility.js";
import emailService from "../utils/emailService.js";
import { combineName, GetCustomer } from "../services/customerGet.service.js";

export const linkPrescriptionWithOrderController = async (req, res) => {
  res.sendStatus(200);
  console.log("LINK PRESCRIPTION WITH ORDER TRIGGER");

  const orderData = req.body;
  console.log(
    "orderData ---------------------------------------------",
    orderData
  );

  // const { orderId, prescriptionId, locale } = req.body;
  // const { storeKey } = req.query;

  const orderId = orderData.id;
  const prescriptionId = orderData.note_attributes.find(
    (x) => x.name.trim() === "prescriptionId"
  )?.value;

  if (!orderId || !prescriptionId) {
    console.log("Note Attributes missing fields");
    return;
  }

  try {
    const isSelfOrder = orderData.note_attributes.find(
      (x) => x.name.trim() === "selfOrder"
    )?.value;
    let phoneUpdated = false;
    if (isSelfOrder && isSelfOrder === "true") {
      const patientphone = orderData.note_attributes.find(
        (x) => x.name.trim() === "patientphone"
      )?.value;

      phoneUpdated = await UpdateCustomerPhone(
        orderData.customer?.admin_graphql_api_id,
        patientphone
      );
    } else {
      const billingPhone = orderData?.billing_address?.phone;
      if (billingPhone) {
        phoneUpdated = await UpdateCustomerPhone(
          orderData.customer?.admin_graphql_api_id,
          billingPhone
        );
      } else {
        const shippingPhone = orderData?.shipping_address?.phone;
        if (shippingPhone) {
          phoneUpdated = await UpdateCustomerPhone(
            orderData.customer?.admin_graphql_api_id,
            shippingPhone
          );
        }
      }
    }
    if (phoneUpdated) {
      console.log("Phone is Updated.");
    } else {
      console.log("Phone Update FAILED.");
    }

    const isDoNotDeliver = await ProcessDoNotDeliver(orderData.line_items);
    if (!isDoNotDeliver) {
      const result = await linkPrescriptionWithOrderService(
        orderId,
        prescriptionId
      );
      console.log("linkPrescriptionWithOrder result ", result);
      if (result?.result) {
        const metaReponse = await fetchGraphqlDataShopify(SET_META, {
          metafields: [
            {
              namespace: "custom",
              key: "order_prescription_linking",
              value: "true",
              type: "boolean",
              ownerId: orderData?.admin_graphql_api_id,
            },
          ],
        });

        // console.log("discountReponse : ", discountReponse)

        const metafieldsArray = metaReponse?.data?.metafieldsSet?.metafields;

        if (metafieldsArray.length !== 0) {
          console.log("Order Linking metafield set success:", metaReponse.data);
        }
      }
    }

    const isPAPOrder = await ProcessPapSku(
      orderData.line_items,
      orderData.customer?.admin_graphql_api_id
    );
    if (isPAPOrder) {
      console.log("Customer meta pap_order set to true");
    } else {
      console.log(
        "Something went wrong for ProcessPapSku func or No PAP SKU found"
      );
    }

    if (!isDoNotDeliver) {
      const customerEmail = orderData.customer?.email;
      if (!customerEmail) {
        console.log("Email for the customer not found...");
        return;
      }

      const freeTrialItem = orderData.line_items.some(
        (lineItem) => lineItem.name === `${process.env.EMAIL_45_DAYS_NAME}`
      );

      const freeTrialItemPAP = orderData.line_items.some(
        (lineItem) => lineItem.name === `${process.env.EMAIL_45_DAYS_NAME_PAP}`
      );

      if (freeTrialItem) {
        // Prepare Free Trial Email Payload
        console.log("Free Trial Email Triggered");

        const freeTrialEmailPayload = {
          first_name: orderData.customer?.first_name || "Customer",
          payment_date: new Date(
            (orderData.created_at
              ? new Date(orderData.created_at).getTime()
              : Date.now()) +
              50 * 24 * 60 * 60 * 1000
          ).toLocaleDateString(),
          orderId: orderData.name,
          price: `${process.env.FREE_TRIAL_PRICE}`,
          order: {
            shippingAddress: {
              additionalStreetInfo: "",
              streetName: orderData.shipping_address?.address1 || "",
              streetNumber: orderData.shipping_address?.address2 || "",
              postalCode: orderData.shipping_address?.zip || "",
              city: orderData.shipping_address?.city || "",
              country: orderData.shipping_address?.country_code || "ES",
            },
          },
        };

        // Send Free Trial Email
        await emailService.sendEmail(
          "orderConfirmationFreeTrial", // Replace with actual Free Trial Email Template ID
          freeTrialEmailPayload,
          customerEmail
        );
      } else if (freeTrialItemPAP) {
        // Prepare Free Trial Email Payload
        console.log("Free Trial PAP Email Triggered");

        const freeTrialPAPEmailPayload = {
          first_name: orderData.customer?.first_name || "Customer",
          payment_date: new Date(
            (orderData.created_at
              ? new Date(orderData.created_at).getTime()
              : Date.now()) +
              50 * 24 * 60 * 60 * 1000
          ).toLocaleDateString(),
          orderId: orderData.name,
          price: `${process.env.FREE_TRIAL_PAP_PRICE}`,
          order: {
            shippingAddress: {
              additionalStreetInfo: "",
              streetName: orderData.shipping_address?.address1 || "",
              streetNumber: orderData.shipping_address?.address2 || "",
              postalCode: orderData.shipping_address?.zip || "",
              city: orderData.shipping_address?.city || "",
              country: orderData.shipping_address?.country_code || "ES",
            },
          },
        };

        // Send Free Trial Email
        await emailService.sendEmail(
          "orderConfirmationFreeTrial", // Replace with actual Free Trial Email Template ID
          freeTrialPAPEmailPayload,
          customerEmail
        );
      } else {
        // Send Regular Order Confirmation Email
        console.log("Send Order Email");
        const emailPayload = await FormatEmailOrderPayload(orderData);
        await emailService.sendEmail(
          "orderConfirmation", // Replace with actual Order Confirmation Email Template ID
          emailPayload,
          customerEmail
        );
      }
    } else {
      // Fulfill Order
      const fulfilmentIds = await CreateFulfillmentForOrderLineItems(
        orderId,
        orderData.line_items
      );
      console.log("Order is fulfilled with Fulfilment Ids : ", fulfilmentIds);
    }
  } catch (error) {
    console.error("Error in linkPrescriptionWithOrderController:", error);
    // res.status(500).send({
    //     result: false,
    //     message: error.message,
    // });
  }
};

async function UpdateCustomerPhone(customerId, phone) {
  try {
    const updatePhoneResponse = await fetchGraphqlDataShopify(
      UPDATE_CUSTOMER_PHONE,
      {
        id: customerId,
        phone: `${phone}`,
      }
    );

    // console.log("discountReponse : ", discountReponse)

    // console.log("updatePhoneResponse : ", updatePhoneResponse.data);

    const cusId = updatePhoneResponse?.data?.customerUpdate?.customer?.id;
    const cusUserErrors = updatePhoneResponse?.data?.customerUpdate?.userErrors;

    if (cusUserErrors && cusUserErrors.length !== 0) {
      console.log(
        "User Error while updating phone : ",
        JSON.stringify(cusUserErrors, undefined, 2)
      );
      return false;
    } else if (cusId) {
      console.log("Customer phone updated");
      return true;
    } else {
      console.log("Error updating customer phone : ", updatePhoneResponse);
      return false;
    }
  } catch (error) {
    console.log("Some Error occured during Phone Update : ", error);
    return false;
  }
}

async function ProcessDoNotDeliver(lineItems) {
  // Check if any line item has the SKU "Donotdeliver"
  const isDoNotDeliver = lineItems.some(
    (item) => item.sku === `${process.env.DO_NOT_DELIVER}`
  );
  return isDoNotDeliver;
}

async function ProcessPapSku(lineItems, customerId) {
  try {
    // Iterate over each line item in the array
    if (!customerId) {
      return false;
    }

    for (const item of lineItems) {
      // Check if 'properties' contains { name: "product_type", value: "Subscription" }
      const subscriptionProperty = item.properties.find(
        (prop) =>
          prop.name === "product_type" &&
          prop.value === `${process.env.PRODUCT_TYPE_SUBSCRIPTION}`
      );

      if (subscriptionProperty) {
        console.log(`Subscription found in SKU: ${item.sku}`);

        const metaReponse = await fetchGraphqlDataShopify(SET_META, {
          metafields: [
            {
              namespace: "custom",
              key: "pap_order",
              value: "true",
              type: "boolean",
              ownerId: customerId,
            },
          ],
        });

        // console.log("discountReponse : ", discountReponse)

        const metafieldsArray = metaReponse?.data?.metafieldsSet?.metafields;

        if (metafieldsArray.length !== 0) {
          console.log("Customer metafield updated:", metaReponse.data);
          return true;
        } else {
          return false;
        }
      } else {
        console.log(`No subscription for SKU: ${item.sku}`);
      }
    }
  } catch (error) {
    console.error("Error in ProcessPapSku Func :", error);
    return false;
  }
}

const getFulfillmentOrdersOfOrder = async (order_id) => {
  try {
    let storeData = await getATFromSQL();
    let AT;
    storeData.map((x) => {
      if (x.shop === process.env.SHOP) {
        AT = x.accessToken;
      }
    });

    let config = {
      method: "get",
      maxBodyLength: Infinity,
      url: `https://${process.env.SHOP}/admin/api/${process.env.GRAPH_QL_API_VERSION}/orders/${order_id}/fulfillment_orders.json`,
      headers: {
        "X-Shopify-Access-Token": AT,
      },
    };

    const response = await axios.request(config);
    if (response.data) {
      const fulfillmentOrders = response.data.fulfillment_orders;
      // .flatMap(
      //   (fulfillment) => fulfillment.line_items
      // );
      return fulfillmentOrders;
    }
    return [];
  } catch (error) {
    console.log(error);
    return [];
  }
};

const ExtractLineItemIds = (lineItems) => {
  return lineItems.map((item) => ({
    id: item.id,
    quantity: item.quantity,
  }));
};

async function CreateFulfillmentForOrderLineItems(orderId, lineItems) {
  try {
    const allFulfillmentOrders = await getFulfillmentOrdersOfOrder(orderId);
    const fulfilment_ids = [];

    console.log("All fulfilments in shopify", allFulfillmentOrders);
    const line_item_ids = ExtractLineItemIds(lineItems);
    console.log("line items ids send to create fulfilment:", line_item_ids);
    if (!line_item_ids || line_item_ids?.length === 0) {
      return [];
    }
    const filteredFulfillmentOrders = allFulfillmentOrders.filter(
      (fulfillment) => {
        const fulfillmentLineItemIds = fulfillment.line_items.map(
          (item) => item.line_item_id
        );
        return line_item_ids.every((item) =>
          fulfillmentLineItemIds.includes(item.id)
        );
      }
    );

    // console.log({ filteredFulfillmentOrders });

    const line_item_fulfillments = filteredFulfillmentOrders.map(
      (fulfillment) => {
        const filteredLineItems = fulfillment.line_items.filter((item) =>
          line_item_ids.find(
            (iitem) => iitem.id.toString() === item.line_item_id.toString()
          )
        );
        const fulfillment_order_line_items = filteredLineItems.map((item) => {
          const correspondingLineItem = line_item_ids.find(
            (iitem) => iitem.id.toString() === item.line_item_id.toString()
          );
          return {
            id: item.id,
            quantity: correspondingLineItem.quantity,
          };
        });
        return {
          fulfillment_order_id: fulfillment.id,
          fulfillment_order_line_items: fulfillment_order_line_items,
        };
      }
    );

    // console.log({ line_item_fulfillments });

    if (line_item_fulfillments.length === 0) {
      console.log(
        "No line_item_fulfillments to create fulfilment for order:",
        orderId
      );
      return fulfilment_ids;
    }

    let storeData = await getATFromSQL();
    let AT;
    storeData.map((x) => {
      if (x.shop === process.env.SHOP) {
        AT = x.accessToken;
      }
    });

    for (let i = 0; i < line_item_fulfillments.length; i++) {
      const line_item_fulfillment = line_item_fulfillments[i];
      let data = JSON.stringify({
        fulfillment: {
          message: "The package is Do not deliver.",
          notify_customer: false,
          line_items_by_fulfillment_order: [line_item_fulfillment],
        },
      });

      let config = {
        method: "post",
        maxBodyLength: Infinity,
        url: `https://${process.env.SHOP}/admin/api/${process.env.GRAPH_QL_API_VERSION}/fulfillments.json`,
        headers: {
          "X-Shopify-Access-Token": AT,
          "Content-Type": "application/json",
        },
        data: data,
      };

      const response = await axios.request(config);

      const fulfillment = response?.data?.fulfillment;
      if (fulfillment) {
        fulfilment_ids.push(fulfillment.id);
      }
    }

    return fulfilment_ids;
  } catch (error) {
    console.log("error while creating fulfilment in Shopify", error);
    console.log("error response : ", error.response.data.errors);
    return [];
  }
}

async function FormatEmailOrderPayload(order) {
  // const variantResponse

  const paymentMethod =
    order.payment_gateway_names[0] === "shopify_payments"
      ? "creditcard"
      : order.payment_gateway_names[0] === "paypal"
      ? "paypal"
      : order.payment_gateway_names[0] === process.env.PAYMENT_MOLLIE ||
        order.payment_gateway_names[0].toLowerCase().includes("sepa")
      ? "banktransfer"
      : order.payment_gateway_names[0] === process.env.PAYMENT_KLARNA ||
        order.payment_gateway_names[0].toLowerCase().includes("klarna")
      ? "klarna"
      : order.payment_gateway_names[0];

  const payload = {
    language: order.client_details.accept_language || "es-ES",
    cartId: order.cart_token,
    orderState: order.order_status_url,
    orderId: order.name,
    orderVersion: "1",
    createdAt: order.created_at,
    formattedDiscountPrice: order.current_total_discounts + " €",
    formattedSubtotalPrice:
      (
        parseFloat(order.current_subtotal_price) -
        parseFloat(order.current_total_tax) -
        parseFloat(order.current_total_discounts)
      ).toFixed(2) + " €",
    formattedVATPrice: order.current_total_tax + " €",
    formattedTotalPrice: order.current_subtotal_price + " €",

    lineItems: order.line_items.map((item) => {
      const basePrice = parseFloat(item.price) * item.current_quantity;
      const totalDiscount = item.discount_allocations.reduce(
        (sum, discount) => sum + parseFloat(discount.amount),
        0
      );
      const totalTax = item.tax_lines.reduce(
        (sum, tax) => sum + parseFloat(tax.price),
        0
      );
      const formattedSubtotalPrice = (
        basePrice -
        totalDiscount -
        totalTax
      ).toFixed(2);

      return {
        lineItemId: item.id,
        productId: item.product_id,
        name: item.title,
        type: "variant",
        count: item.quantity,
        formattedPrice: item.price + " €",
        formattedSubtotalPrice: formattedSubtotalPrice + " €",
        variant: {
          id: item.variant_id,
          sku: item.sku,
        },
        price: {
          type: "centPrecision",
          currencyCode: "EUR",
          centAmount: parseInt(item.price * 100),
          fractionDigits: 2,
        },
        totalPrice: {
          type: "centPrecision",
          currencyCode: "EUR",
          centAmount: parseInt(item.price * item.quantity * 100),
          fractionDigits: 2,
        },
      };
    }),
    email: order.customer.email,
    shippingAddress: {
      firstName: order.shipping_address?.first_name || "",
      lastName: order.shipping_address?.last_name || "",
      streetName: order.shipping_address?.address1 || "",
      streetNumber: order.shipping_address?.address2 || "",
      additionalStreetInfo: "",
      additionalAddressInfo: "",
      postalCode: order.shipping_address?.zip || "",
      city: order.shipping_address?.city || "",
      country: order.shipping_address?.country_code || "ES",
      state: order.shipping_address?.province || "",
    },
    billingAddress: {
      firstName: order.billing_address?.first_name || "",
      lastName: order.billing_address?.last_name || "",
      streetName: order.billing_address?.address1 || "",
      streetNumber: order.billing_address?.address2 || "",
      postalCode: order.billing_address?.zip || "",
      city: order.billing_address?.city || "",
      country: order.billing_address?.country_code || "ES",
      state: order.billing_address?.province || "",
    },
    sum: {
      fractionDigits: 2,
      centAmount: 0,
      currencyCode: "EUR",
    },
    type: "Order",
    payment: {
      paymentMethodName: paymentMethod,
    },
    payments: [],
    taxed: {
      amount: {
        currencyCode: "EUR",
        centAmount: parseInt(order.total_price * 100),
        fractionDigits: 2,
      },
      taxPortions: order.tax_lines.map((tax_line) => ({
        rate: tax_line.rate,
        amount: {
          currencyCode: "EUR",
          centAmount: parseInt(tax_line.price * 100), // Convert price to cents
          fractionDigits: 2,
        },
        name: tax_line.title,
      })),
    },
    directDiscountCodes: [],
    shippingInfo: {
      shippingMethodId: order.shipping_lines[0].id,
      name: order.shipping_lines[0].title,
      formattedPrice: order.shipping_lines[0].price + " €",
      price: {
        fractionDigits: 2,
        centAmount: parseInt(order.shipping_lines[0].price * 100),
        currencyCode: "EUR",
      },
      deliveries: [],
    },
  };
  return payload;
}
