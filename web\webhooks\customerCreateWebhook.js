import axios from "axios";
import { getATFromSQL } from "../utils/utility.js";

export async function customerCreationWebhook() {
  let storeData = await getATFromSQL();
  let AT;
  storeData.map((x) => {
    if (x.shop === process.env.SHOP) {
      AT = x.accessToken;
    }
  });

  console.log("AT : ", AT);

  await axios
    .request({
      method: "post",
      //   url: `${process.env.SHOP_URL}/2023-10/graphql.json`,
      url: `${process.env.SHOP_URL}/admin/api/graphql.json`,
      headers: {
        "X-Shopify-Access-Token": AT,
        "Content-Type": "application/json",
      },
      data: JSON.stringify({
        query: `mutation MyMutation {
          webhookSubscriptionCreate(
            webhookSubscription: {callbackUrl: "${process.env.HOST}/api/webhook/customerCreateWebhook", format: JSON}
            topic: CUSTOMERS_CREATE
          ) {
            userErrors {
              field
              message
            }
            webhookSubscription {
               id
               updatedAt
               includeFields
               createdAt
               callbackUrl
               format
               topic
            }
          }
        }`,
        variables: {},
      }),
    })
    .then((response) => {
      console.log(
        "CUSTOMER CREATED WEBHOOK",
        response.data.data.webhookSubscriptionCreate
      );
      // console.log("ORDER CREATED WEBHOOK RESPONSE DATA", JSON.stringify(response.data, null, 2));
      return true;
    })
    .catch((error) => {
      console.log(error, " errorrieving webhook subscription");
      return error;
    });
}
