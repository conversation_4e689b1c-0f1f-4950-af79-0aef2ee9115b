export const AllowedOrigin = async (req, res, next) => {
  if (process.env.NODE_ENV == "developement") {
    /* if it is in development environment bypass the AllowedOrigin */
    res.setHeader("Access-Control-Allow-Origin", "*");
    res.setHeader("Access-Control-Allow-Headers", "*");
    res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE");
    next();
  }
  //will update when we have frontend deployed url
  const allowedOrigins = [`https://nerivio-spain.myshopify.com`];
  const requestOrigin = req.headers.origin;
  console.log("requestOrigin:", requestOrigin);

  // Check if the request's origin is in the list of allowed origins
  if (allowedOrigins.includes(requestOrigin)) {
    // Allow the request's origin
    res.setHeader("Access-Control-Allow-Origin", requestOrigin);
  } else {
    // Block all other origins
    res.setHeader("Access-Control-Allow-Origin", null);
  }

  // Set additional CORS headers for preflight requests
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE");
  // Allow all headers
  res.setHeader("Access-Control-Allow-Headers", "*");

  next();
};
