import axios from "axios";
import { getOAuthToken } from "../utils/generateOauthToken.js";
import emailService from "../utils/emailService.js";
import { getUserDataByEmail } from "./customerCreate.servive.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";
import { DELETE_CUSTOMER_BY_ID } from "../helpers/graphqlApis.js";
import { ConvertToGID } from "../controllers/discount.controller.js";

const customerDeleteService = async (email) => {
  try {
    const emailSent = await emailService.sendEmail(
      "accountDeletion",
      {
        customer: {
          email: email,
        },
        locale: process.env.LOCALE,
        store: process.env.STOREKEY,
      },
      `${process.env.NERIVIO_ADMIN_EMAIL}`
    );
    if (emailSent) {
      return true;
    } else {
      return false;
    }

    // const userData = await getUserDataByEmail(email);
    // const userId = userData[0]?.user_id;
    // let FetchedBearerToken = await getOAuthToken();
    // const customerGid = ConvertToGID(customerId, "Customer");
    // const customerReponse = await fetchGraphqlDataShopify(
    //   DELETE_CUSTOMER_BY_ID,
    //   {
    //     id: customerGid,
    //   }
    // );

    // // console.log("customerReponse : ", customerReponse)

    // const deleteCustomerID =
    //   customerReponse?.data?.customerDelete?.deletedCustomerId;
    // if (!deleteCustomerID) {
    //   console.log("Customer Delete in Shopify failed");
    //   return null;
    // }

    // const response = await axios({
    //   method: "delete",
    //   url: `https://${process.env.OKTA_OAUTH_BASE_URL}/api/v2/users/${userId}`,
    //   headers: {
    //     "Content-Type": "application/json",
    //     Authorization: `Bearer ${FetchedBearerToken}`,
    //   },
    // });

    // return response.data;
  } catch (error) {
    console.error("Error sending password reset email:", error);
    return null;
  }
};

export { customerDeleteService };
