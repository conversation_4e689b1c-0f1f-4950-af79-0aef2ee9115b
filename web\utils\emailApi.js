import EmailErrorMapper from "./emailMapper.js";
import sgMail from "@sendgrid/mail";

const configuration = {
  clientHost: process.env.CLIENT_HOST || "https://example.com",
  emailTemplates: {
    accountVerification: process.env.ACCOUNT_VERIFICATION_TEMPLATE_ID,
    passwordReset: process.env.PASSWORD_RESET_TEMPLATE_ID,
    orderConfirmation: process.env.ORDER_CONFIRMATION_TEMPLATE_ID,
    orderConfirmationFreeTrial:
      process.env.ORDER_CONFIRMATION_FREE_TRIAL_TEMPLATE_ID,
    welcomeCustomer: process.env.WELCOME_CUSTOMER_TEMPLATE_ID,
    accountDeletion: process.env.ACCOUNT_DELETION_TEMPLATE_ID,
    paymentSynchronizationIssue: process.env.PAYMENT_SYNC_ISSUE_TEMPLATE_ID,
    failedOrder: process.env.FAILED_ORDER_TEMPLATE_ID,
    supportEnrollmentConfirmation: process.env.SUPPORT_ENROLLMENT_TEMPLATE_ID,
    supportUnenrollmentConfirmation:
      process.env.SUPPORT_UNENROLLMENT_TEMPLATE_ID,
  },
  fromEmail: process.env.FROM_EMAIL,
};

class EmailApi {
  constructor(context) {
    this.context = context;
    this.configuration = configuration;
    sgMail.setApiKey(`${process.env.SENDGRID_API_KEY}`);
  }

  getDynamicTemplateData(type, payload) {
    switch (type) {
      case "accountVerification":
        return {
          customer: payload.customer,
          url: `${this.configuration.clientHost}/verify?token=${payload.token}`,
        };
      case "passwordReset":
        return {
          customer: payload.customer,
          url: `${this.configuration.clientHost}/reset-password?token=${payload.token}`,
        };
      case "orderConfirmationFreeTrial":
        return payload;
      case "orderConfirmation":
        // const discount = {
        //   // centAmount: this.getTotalDiscounts(payload.order),
        //   currencyCode: payload?.order?.sum?.currencyCode,
        //   fractionDigits: payload?.order?.sum?.fractionDigits,
        // };
        // const payload1 = {
        //   language: "es-ES",
        //   order: {
        //     payment: {
        //       paymentMethodName: "creditcard",
        //     },
        //     store: "spain",
        //     formattedDiscountPrice: "30 €",
        //     type: "Order",
        //     orderId: "39dad6a8-b975-4eec-8fe1-a4bd0df7b674",
        //     version: 1,
        //     versionModifiedAt: "2023-12-29T10:41:36.249Z",
        //     lastMessageSequenceNumber: 1,
        //     createdAt: "2023-12-29T10:41:36.213Z",
        //     lastModifiedAt: "2023-12-29T10:41:36.213Z",
        //     lastModifiedBy: {
        //       clientId: "6W4hQUQDiOupSZCvMxX79f-p",
        //       isPlatformClient: false,
        //     },
        //     createdBy: {
        //       clientId: "6W4hQUQDiOupSZCvMxX79f-p",
        //       isPlatformClient: false,
        //     },
        //     orderNumber: "a41ae43a-807c-42e5-2365-71ef34d1f615",
        //     formattedSubtotalPrice: "504.20 €",
        //     formattedVATPrice: "95.80 €",
        //     formattedTotalPrice: "600.00 €",
        //     customerId: "577a6a44-0275-4045-96a4-58478540b3bc",
        //     customerEmail: "<EMAIL>",
        //     totalPrice: {
        //       type: "centPrecision",
        //       currencyCode: "EUR",
        //       centAmount: 60000,
        //       fractionDigits: 2,
        //     },
        //     taxedPrice: {
        //       totalNet: {
        //         type: "centPrecision",
        //         currencyCode: "EUR",
        //         centAmount: 50420,
        //         fractionDigits: 2,
        //       },
        //       totalGross: {
        //         type: "centPrecision",
        //         currencyCode: "EUR",
        //         centAmount: 60000,
        //         fractionDigits: 2,
        //       },
        //       taxPortions: [
        //         {
        //           rate: 0.19,
        //           amount: {
        //             type: "centPrecision",
        //             currencyCode: "EUR",
        //             centAmount: 9580,
        //             fractionDigits: 2,
        //           },
        //           name: "VAT_DE",
        //         },
        //       ],
        //       totalTax: {
        //         type: "centPrecision",
        //         currencyCode: "EUR",
        //         centAmount: 9580,
        //         fractionDigits: 2,
        //       },
        //     },
        //     country: "DE",
        //     orderState: "Open",
        //     syncInfo: [],
        //     returnInfo: [],
        //     taxMode: "Platform",
        //     inventoryMode: "ReserveOnOrder",
        //     taxRoundingMode: "HalfEven",
        //     taxCalculationMode: "LineItemLevel",
        //     origin: "Customer",
        //     shippingMode: "Single",
        //     shippingInfo: {
        //       name: "Standard",
        //       formattedPrice: "0 €",
        //       price: {
        //         type: "centPrecision",
        //         currencyCode: "EUR",
        //         centAmount: 0,
        //         fractionDigits: 2,
        //       },
        //       shippingRate: {
        //         price: {
        //           type: "centPrecision",
        //           currencyCode: "EUR",
        //           centAmount: 0,
        //           fractionDigits: 2,
        //         },
        //         tiers: [],
        //       },
        //       taxRate: {
        //         name: "VAT_DE",
        //         amount: 0.19,
        //         includedInPrice: true,
        //         country: "DE",
        //         id: "ld8mROjP",
        //         key: "VAT_DE",
        //         subRates: [],
        //       },
        //       taxCategory: {
        //         typeId: "tax-category",
        //         id: "b9b392d8-7fd5-46d8-89fc-68db24213954",
        //       },
        //       deliveries: [],
        //       shippingMethod: {
        //         typeId: "shipping-method",
        //         id: "175c930f-4a2d-4bf2-a2b4-6f370698ed96",
        //       },
        //       taxedPrice: {
        //         totalNet: {
        //           type: "centPrecision",
        //           currencyCode: "EUR",
        //           centAmount: 0,
        //           fractionDigits: 2,
        //         },
        //         totalGross: {
        //           type: "centPrecision",
        //           currencyCode: "EUR",
        //           centAmount: 0,
        //           fractionDigits: 2,
        //         },
        //         totalTax: {
        //           type: "centPrecision",
        //           currencyCode: "EUR",
        //           centAmount: 0,
        //           fractionDigits: 2,
        //         },
        //       },
        //       shippingMethodState: "MatchesCart",
        //     },
        //     shippingAddress: {
        //       id: "dhl-address",
        //       firstName: "Thach",
        //       lastName: "Dang",
        //       streetName: "Am campus",
        //       streetNumber: "5",
        //       additionalStreetInfo: "",
        //       postalCode: "48721",
        //       city: "Gescher",
        //       country: "ES",
        //       additionalAddressInfo: "12515555",
        //     },
        //     shipping: [],
        //     lineItems: [
        //       {
        //         id: "aaccdc41-6bdf-4e70-8cf7-7da94394f91a",
        //         productId: "a6a8ff6b-6d3a-4992-86e9-dda43d6beaec",
        //         productKey: "nerivio",
        //         lineItemName: "Nerivio",
        //         formattedPrice: "600.00 €",
        //         formattedTotalPrice: "600.00 €",
        //         name: "Nerivio",
        //         productType: {
        //           typeId: "product-type",
        //           id: "6e230e8d-e1b0-4c91-acae-e710eecf9b2a",
        //           version: 1,
        //         },
        //         productSlug: {
        //           "de-DE": "get-nerivio",
        //           "en-GB": "get-nerivio",
        //         },
        //         variant: {
        //           id: 1,
        //           sku: "betapharm-nerivio",
        //           key: "betapharm-nerivio",
        //           prices: [
        //             {
        //               id: "18615d0a-4ee4-48d0-a8ee-43259b571900",
        //               value: {
        //                 type: "centPrecision",
        //                 currencyCode: "EUR",
        //                 centAmount: 60000,
        //                 fractionDigits: 2,
        //               },
        //               country: "DE",
        //             },
        //             {
        //               id: "e0cc1fb6-cf76-46ba-a9ee-27d413130ddd",
        //               value: {
        //                 type: "centPrecision",
        //                 currencyCode: "EUR",
        //                 centAmount: 60000,
        //                 fractionDigits: 2,
        //               },
        //               country: "GB",
        //             },
        //           ],
        //           images: [
        //             {
        //               url: "https://4b5e31b977950139c1e3-0d3d0e05bce1515f743963f141daa818.ssl.cf5.rackcdn.com/Nerivio-product-6E7iwl4G.png",
        //               label: "",
        //               dimensions: {
        //                 w: 745,
        //                 h: 671,
        //               },
        //             },
        //           ],
        //           attributes: [
        //             {
        //               name: "brand",
        //               value: {
        //                 "de-DE": "Betapharm Nerivio",
        //                 "en-GB": "Betapharm Nerivio",
        //               },
        //             },
        //             {
        //               name: "long-description",
        //               value: {
        //                 "en-GB":
        //                   "Introducing Nerivio – Your Personalized Migraine Relief Solution\n\nMigraineGuard offers advanced relief through trigger detection and tailored therapies. Non-invasive, real-time monitoring and a user-friendly app empower you to manage migraines effectively. Regain control and rediscover a life free from debilitating pain. Visit us now for a liberating experience.",
        //                 "de-DE":
        //                   "Nerivio – Ihre individuelle Migränelinderung\n\nErleben Sie Fortschritt durch Auslösererkennung und maßgeschneiderte Therapien. Nicht-invasive Echtzeitüberwachung und benutzerfreundliche App geben Ihnen die Kontrolle zurück. Befreien Sie sich von quälenden Schmerzen. Entdecken Sie jetzt die Freiheit bei uns.",
        //               },
        //             },
        //             {
        //               name: "unit-of-measure",
        //               value: {
        //                 "de-DE": "1",
        //                 "en-GB": "1",
        //               },
        //             },
        //           ],
        //           assets: [],
        //           availability: {
        //             isOnStock: true,
        //             availableQuantity: 9999999700,
        //             version: 78,
        //             id: "639b67a0-234b-4f54-a32c-62cad35c6e9b",
        //           },
        //         },
        //         price: {
        //           id: "18615d0a-4ee4-48d0-a8ee-43259b571900",
        //           value: {
        //             type: "centPrecision",
        //             currencyCode: "EUR",
        //             centAmount: 60000,
        //             fractionDigits: 2,
        //           },
        //           country: "DE",
        //         },
        //         count: 3,
        //         discountedPricePerQuantity: [],
        //         taxRate: {
        //           name: "VAT_DE",
        //           amount: 0.19,
        //           includedInPrice: true,
        //           country: "DE",
        //           id: "ld8mROjP",
        //           key: "VAT_DE",
        //           subRates: [],
        //         },
        //         perMethodTaxRate: [],
        //         addedAt: "2023-12-28T15:44:46.048Z",
        //         lastModifiedAt: "2023-12-28T15:44:46.048Z",
        //         state: [
        //           {
        //             quantity: 1,
        //             state: {
        //               typeId: "state",
        //               id: "9c9c77aa-122c-4087-9595-f1bcb35300b2",
        //             },
        //           },
        //         ],
        //         priceMode: "Platform",
        //         lineItemMode: "Standard",
        //         totalPrice: {
        //           type: "centPrecision",
        //           currencyCode: "EUR",
        //           centAmount: 60000,
        //           fractionDigits: 2,
        //         },
        //         taxedPrice: {
        //           totalNet: {
        //             type: "centPrecision",
        //             currencyCode: "EUR",
        //             centAmount: 50420,
        //             fractionDigits: 2,
        //           },
        //           totalGross: {
        //             type: "centPrecision",
        //             currencyCode: "EUR",
        //             centAmount: 60000,
        //             fractionDigits: 2,
        //           },
        //           totalTax: {
        //             type: "centPrecision",
        //             currencyCode: "EUR",
        //             centAmount: 9580,
        //             fractionDigits: 2,
        //           },
        //         },
        //         taxedPricePortions: [],
        //       },
        //     ],
        //     customLineItems: [],
        //     transactionFee: true,
        //     discountCodes: [],
        //     directDiscounts: [],
        //     cart: {
        //       typeId: "cart",
        //       id: "f5bc1beb-a74e-4db9-81c3-546ec8906748",
        //     },
        //     paymentInfo: {
        //       payments: [
        //         {
        //           typeId: "payment",
        //           id: "8692d3aa-85ff-439b-a3bd-34a345c866cd",
        //         },
        //         {
        //           typeId: "payment",
        //           id: "f668cc8c-6082-47e1-9945-54967e069bb9",
        //         },
        //         {
        //           typeId: "payment",
        //           id: "5557b8ad-3818-4ab5-aa18-6493e74c9530",
        //         },
        //       ],
        //     },
        //     billingAddress: {
        //       id: "1st-shipping-address",
        //       firstName: "Thach",
        //       lastName: "Dang",
        //       streetName: "Am campus",
        //       streetNumber: "5",
        //       additionalStreetInfo: "",
        //       postalCode: "48721",
        //       city: "Gescher",
        //       country: "ES",
        //       additionalAddressInfo: "6124577",
        //     },
        //     itemShippingAddresses: [],
        //     refusedGifts: [],
        //   },
        // };
        return {
          language: payload.language,
          order: payload,
        };
      case "welcomeCustomer":
        return { customer: payload.customer };
      case "accountDeletion":
        return {
          customer_email: payload.customer.email,
          language: payload.locale,
          store: payload.store,
        };
      case "paymentSynchronizationIssue":
        return {
          cart: payload.cart,
          payment: payload.payment,
        };
      case "failedOrder":
        const errorData = EmailErrorMapper.mapError(payload.error);
        return {
          cart: payload.cart,
          errorData,
        };
      case "supportEnrollmentConfirmation":
        return { patient_name: payload.patientName };
      case "supportUnenrollmentConfirmation":
        return { patient_name: payload.patientName };
      default:
        console.error(`Unknown email type: ${type}`);
        return {};
    }
  }

  async sendEmail(type, payload, email) {
    try {
      // Send the email using SendGrid
      const templateId = this.configuration.emailTemplates[type];
      const dynamicTemplateData = this.getDynamicTemplateData(type, payload);
      const fromEmail = this.configuration.fromEmail;

      if (!templateId) {
        console.error(`Template ID not found for email type: ${type}`);
        return false;
      }

      // Build the email data for SendGrid
      const emailData = {
        to: email,
        from:
          type === "orderConfirmation" || type === "orderConfirmationFreeTrial"
            ? `${process.env.FROM_EMAIL_ORDER}`
            : type === "supportEnrollmentConfirmation" ||
              type === "supportUnenrollmentConfirmation"
            ? `${process.env.NERIVIO_ADMIN_EMAIL}`
            : fromEmail,
        templateId: templateId,
        dynamicTemplateData: dynamicTemplateData,
      };

      await sgMail.send(emailData);
      console.log(`Email sent successfully to ${email}`);
      return true;
    } catch (error) {
      console.error("Error sending email:", error);
      console.error(
        "Error sending email exact :",
        JSON.stringify(error?.response?.body?.errors, undefined, 2)
      );
      return false;
    }
  }
}

export default EmailApi;
