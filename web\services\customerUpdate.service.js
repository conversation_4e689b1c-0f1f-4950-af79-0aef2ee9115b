import axios from "axios";
import { getATFromSQL } from "../utils/utility.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";
import { SET_META } from "../helpers/graphqlApis.js";
import { ConvertToGID } from "../controllers/discount.controller.js";
import getDate12MonthsFromNow from "../helpers/getDate12Months.js";

const updateMetafieldsDateOfBirth = async (customerId, dateofbirth, AT) => {
  const query = `
    mutation {
      metafieldsSet(
        metafields: {
          ownerId: "gid://shopify/Customer/${customerId}", 
          key: "date_of_birth", 
          value: "${dateofbirth}", 
          namespace: "custom", 
          type: "date"
        }
      ) {
        userErrors {
          field
          message
        }
        metafields {
          id
          createdAt
          namespace
          type
          value
        }
      }
    }
  `;

  try {
    const response = await axios({
      method: "post",
      url: `https://${process.env.SHOP}/admin/api/${process.env.GRAPH_QL_API_VERSION}/graphql.json`,
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": AT,
      },
      data: JSON.stringify({ query }),
    });

    return response.data;
  } catch (error) {
    console.error("Error updating date of birth metafield:", error);
    return null;
  }
};

export const updateCustomerDetailsService = async (
  customerId,
  firstName,
  lastName,
  phone,
  dateofbirth
) => {
  let storeData = await getATFromSQL();
  let AT;
  storeData.map((x) => {
    if (x.shop === process.env.SHOP) {
      AT = x.accessToken;
    }
  });

  let dobmetafieldResult = null;
  if (dateofbirth && customerId) {
    dobmetafieldResult = await updateMetafieldsDateOfBirth(
      customerId,
      dateofbirth,
      AT
    );
  }

  let query = `
  mutation {
    customerUpdate(
      input: {
        firstName: "${firstName}", 
        lastName: "${lastName}", 
        id: "gid://shopify/Customer/${customerId}"
      }
    ) {
      userErrors {
        field
        message
      }
      customer {
        firstName
        lastName
        phone
        email
          id
        updatedAt
      }
    }
  }
`;

  if (phone) {
    query = `
    mutation {
      customerUpdate(
        input: {
          firstName: "${firstName}", 
          lastName: "${lastName}", 
          phone: "${phone}", 
          id: "gid://shopify/Customer/${customerId}"
        }
      ) {
        userErrors {
          field
          message
        }
        customer {
          firstName
          lastName
          phone
          email
            id
          updatedAt
        }
      }
    }
  `;
  }

  try {
    const response = await axios({
      method: "post",
      url: `https://${process.env.SHOP}/admin/api/${process.env.GRAPH_QL_API_VERSION}/graphql.json`,
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": AT,
      },
      data: JSON.stringify({ query }),
    });

    return {
      customerUpdateResult: response.data.data,
      dobUpdateResult: dobmetafieldResult.data,
    };
  } catch (error) {
    console.error("Error updating customer details:", error);
    return null;
  }
};

export const updateCustomerPAPService = async (customerId) => {
  try {
    const shopifyCustomerId = ConvertToGID(customerId, "Customer");
    const date12Months = getDate12MonthsFromNow();
    const metaReponse = await fetchGraphqlDataShopify(SET_META, {
      metafields: [
        {
          namespace: "custom",
          key: "pap_identifier",
          value: "true",
          type: "boolean",
          ownerId: shopifyCustomerId,
        },
        {
          namespace: "custom",
          key: "pap_expiry",
          value: date12Months,
          type: "date",
          ownerId: shopifyCustomerId,
        },
      ],
    });

    const metafieldsArray = metaReponse?.data?.metafieldsSet?.metafields;

    if (metafieldsArray.length !== 0) {
      console.log(
        "Customer pap metafield updated:",
        JSON.stringify(metaReponse.data, undefined, 2)
      );
      return {
        customerUpdatePAPResult: metaReponse.data,
      };
    } else {
      return null;
    }
  } catch (error) {
    console.error("Error updating customer pap details:", error);
    return null;
  }
};
