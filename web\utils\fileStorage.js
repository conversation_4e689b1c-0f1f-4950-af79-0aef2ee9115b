// import multer from "multer";
// import path from "path";
// import fs from "fs";

// // Configure disk storage
// const storage = multer.diskStorage({
//   destination: (req, file, cb) => {
//     const uploadDir = "./uploads";
//     if (!fs.existsSync(uploadDir)) {
//       fs.mkdirSync(uploadDir, { recursive: true }); // Ensure the upload directory exists
//     }
//     cb(null, uploadDir); // Set the directory where files will be saved
//   },
//   filename: (req, file, cb) => {
//     const timestamp = Date.now(); // Add a timestamp to make filenames unique
//     const ext = path.extname(file.originalname); // Extract file extension
//     const fileName = `${file.fieldname}-${timestamp}${ext}`;
//     cb(null, fileName); // Set the filename
//   },
// });

// const upload = multer({
//   storage: storage,
//   limits: { fileSize: 7 * 1024 * 1024 }, // Max file size: 7MB
//   fileFilter: (req, file, cb) => {
//     const allowedTypes = ["image/jpeg", "image/png", "application/pdf"];
//     if (!allowedTypes.includes(file.mimetype)) {
//       return cb(
//         new Error("Invalid file type. Only .jpeg, .png, and .pdf are allowed.")
//       );
//     }
//     cb(null, true);
//   },
// });

// const multerErrorHandler = (err, req, res, next) => {
//   if (err instanceof multer.MulterError) {
//     return res
//       .status(400)
//       .json({ code: "01", result: false, message: err.message });
//   } else if (err) {
//     return res
//       .status(400)
//       .json({ code: "01", result: false, message: err.message });
//   }
// };

// export { upload, multerErrorHandler };

import multer from "multer";

const storage = multer.memoryStorage(); // Use memory storage instead of disk storage

const upload = multer({
  storage: storage,
  limits: { fileSize: 7 * 1024 * 1024 }, // Max file size: 7MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/raw",
      "image/heic",
      "application/pdf",
      "application/octet-stream"
    ];
    if (!allowedTypes.includes(file.mimetype)) {
      return cb(
        new Error(
          "Invalid file type. Only .jpeg, .png, .raw, .heic, and .pdf are allowed."
        )
      );
    }
    cb(null, true);
  },
});

const multerErrorHandler = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    return res
      .status(400)
      .json({ code: "01", result: false, message: err.message });
  } else if (err) {
    return res
      .status(400)
      .json({ code: "01", result: false, message: err.message });
  }
};

export { upload, multerErrorHandler };
