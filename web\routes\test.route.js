// import { Router } from 'express';
// import { multerErrorHandler, upload } from '../utils/fileStorage.js';
// import { updateCustomerDetailsController } from '../controllers/customerUpdate.controller.js';
// import { uploadCustomerPrescriptionController } from '../controllers/prescriptionUpload.controller.js';
// import { customerPatientSupportOptMetafieldController } from '../controllers/customerPatientSupportMeta.controller.js';
// import { customerPasswordResetController } from '../controllers/customerPasswordReset.controller.js';
// import { customerCreateWebhookController } from '../controllers/customerCreateWebhook.controller.js';
// import { linkPrescriptionWithOrderController } from '../controllers/prescriptionOrderLink.controller.js';

// const testRouter = Router();

// // Frontend Endpoints
// testRouter.post("/customerUpdate", updateCustomerDetailsController);

// testRouter.post("/prescriptionUpload", upload.single('file'), multer<PERSON>rrorHandler, uploadCustomerPrescriptionController);

// testRouter.post('/customerPatientSupportOpt', customerPatientSupportOptMetafieldController);

// testRouter.post('/customerPasswordReset', customerPasswordResetController);

// // WEBHOOK ROUTES/ENDPOINTS
// testRouter.post('/customerCreateWebhook', customerCreateWebhookController);

// //ORDER CREATE WEBHOOK RESPONSE
// testRouter.post('/linkPrescriptionWithOrder', linkPrescriptionWithOrderController);

// export default testRouter;
