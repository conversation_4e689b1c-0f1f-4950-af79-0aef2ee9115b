import { Router } from "express";
import {
  updateCustomerDetailsController,
  updateCustomerPAPController,
} from "../controllers/customerUpdate.controller.js";
import { uploadCustomerPrescriptionController } from "../controllers/prescriptionUpload.controller.js";
import { multerErrorHandler, upload } from "../utils/fileStorage.js";
import { customerPatientSupportOptMetafieldController } from "../controllers/customerPatientSupportMeta.controller.js";
import { customerPasswordResetController } from "../controllers/customerPasswordReset.controller.js";
import { verifyProxy } from "../middlewares/proxyVerify.js";
import { customerDeleteController } from "../controllers/customerDelete.controller.js";
import { getOrderInvoiceController } from "../controllers/orderInvoice.controller.js";

const pvsRouter = Router();

// FRONTEND ENDPOINTS/ROUTES
pvsRouter.post("/customerUpdate", verifyProxy, updateCustomerDetailsController);

pvsRouter.post("/customerUpdatePap", verifyProxy, updateCustomerPAPController);

pvsRouter.post(
  "/prescriptionUpload",
  verifyProxy,
  upload.single("file"),
  multerErrorHandler,
  uploadCustomerPrescriptionController
);

pvsRouter.post(
  "/customerPatientSupportOpt",
  verifyProxy,
  customerPatientSupportOptMetafieldController
);

pvsRouter.post(
  "/customerPasswordReset",
  verifyProxy,
  customerPasswordResetController
);

pvsRouter.post("/customerDelete", verifyProxy, customerDeleteController);

pvsRouter.post("/getOrderInvoice", verifyProxy, getOrderInvoiceController);

export default pvsRouter;
