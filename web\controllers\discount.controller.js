import { GET_DISCOUNT_ID_BY_CODE } from "../helpers/graphqlApis.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";

export async function DiscountIdFromCode(req, res) {
  try {
    console.log("####### DiscountIdFromCode Func ######");
    const { discountCode } = req.body; // Extract discountCode from request body

    // Validate that email is provided
    if (!discountCode) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "VALIDATION_ERROR",
            message: "Request body field is required: discountCode",
            field: "discountCode",
          },
        ],
      });
    }

    const discountReponse = await fetchGraphqlDataShopify(
      GET_DISCOUNT_ID_BY_CODE,
      {
        code: discountCode.toString(),
      }
    );

    // console.log("discountReponse : ", discountReponse)

    const discountID = discountReponse?.data?.codeDiscountNodeByCode?.id;
    // const usage =
    //   discountReponse?.data?.codeDiscountNodeByCode?.codeDiscount
    //     ?.asyncUsageCount;

    if (!discountID) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "SHOPIFY_API_FAILURE",
            message: "Discount for the Code not found",
          },
        ],
      });
    }

    // if (usage > 0) {
    //   return res.status(400).json({
    //     responseCode: 1,
    //     status: "error",
    //     errors: [
    //       {
    //         code: "SHOPIFY_API_FAILURE",
    //         message: "Discount is already used",
    //       },
    //     ],
    //   });
    // }

    return res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        discountID: ConvertFromGID(discountID),
      },
    });
  } catch (error) {
    console.error(`Error in DiscountIdFromCode: ${error}`);
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "INTERNAL_SERVER_ERROR",
          message: `An unexpected error occurred: ${error.message}`,
        },
      ],
    });
  }
}

export function ConvertFromGID(gid) {
  // Split the GID string by "/" and return the last part (the numerical ID)
  const parts = gid.split("/");
  return parts[parts.length - 1];
}

export function ConvertToGID(id, identifier) {
  return `gid://shopify/${identifier}/${id}`;
}
